# Load required libraries
library(pacman)
p_load(pacman, filibustr, ggplot2, tidyverse, brms, dplyr)

# Download data for the 117th Senate (adjust `117` to another Congress if you like)
url <- "https://voteview.com/static/data/out/votes/S117_votes.csv"
votes_raw <- read.csv(url)

# Keep only final “Yea” (1) and “Nay” (6) votes
votes_filtered <- votes_raw %>%
  filter(cast_code %in% c(1, 6))


# Spread so each row is a legislator, each column a roll call
vote_matrix <- votes_filtered %>%
  select(icpsr, rollnumber, cast_code) %>%
  pivot_wider(
    names_from  = rollnumber,
    values_from = cast_code
  ) %>%
  arrange(icpsr)

# Recode Yea/Nay to 1/0, leaving other codes (e.g. absences) as NA
vote_matrix[,-1] <- lapply(vote_matrix[,-1], function(x)
  ifelse(x == 1, 1,
    ifelse(x == 6, 0, NA)
  )
)

# Convert to long format
votes_long <- vote_matrix %>%
  pivot_longer(-icpsr, names_to = "rollcall", values_to = "vote") %>%
  filter(!is.na(vote)) %>%
  mutate(rollcall = factor(rollcall),
         legislator = factor(icpsr))



# Identify the 50 bills with the most non‑missing votes
top_rollcalls <- vote_matrix %>%
  select(-icpsr) %>%
  summarise(across(everything(), ~ sum(!is.na(.)))) %>%
  pivot_longer(everything(), names_to = "rollcall", values_to = "n_votes") %>%
  arrange(desc(n_votes)) %>%
  slice_head(n = 50) %>%
  pull(rollcall)

# Build a “short” long‐form dataset on just those 50 roll calls
votes_short <- vote_matrix %>%
  select(icpsr, all_of(top_rollcalls)) %>%
  pivot_longer(-icpsr, names_to = "rollcall", values_to = "vote") %>%
  filter(!is.na(vote)) %>%
  mutate(
    rollcall   = factor(rollcall),
    legislator = factor(icpsr),
    vote        # stays 0/1
  )



# Set up the model formula
formula_2pl_nl <- bf(
  vote ~ inv_logit(exp(logalpha) * eta),
  eta      ~ 1 + (1 | legislator),         # Ideal point per legislator
  logalpha ~ 1 + (1 | i | rollcall),       # Discrimination per roll‑call
  nl = TRUE
)

# Set up weakly informative priors 
priors_2pl_nl <- c(
  prior(normal(0, 1), nlpar = "eta"),
  prior(normal(0, 1), class = "sd", group = "legislator", nlpar = "eta"),
  prior(normal(0, 1), nlpar = "logalpha"),
  prior(normal(0, 1), class = "sd", group = "rollcall", nlpar = "logalpha")
)


model_2pl_nl <- brm(
  formula = formula_2pl_nl,
  data    = votes_short,
  family  = bernoulli(),
  prior   = priors_2pl_nl,
  chains  = 4,
  cores   = 4,
  iter    = 400,
  warmup  = 200,
  control = list(adapt_delta = 0.95)
)

variables(model_2pl_nl)

post <- as_draws_df(model_2pl_nl)

# 1. Pull out the r_legislator__eta[legislator,Intercept] columns
theta_draws <- post %>%
  select(starts_with("r_legislator__eta["))

# 2. Pivot long, extract ICPSR from the name “r_legislator__eta[123,Intercept]”
theta_long <- theta_draws %>%
  pivot_longer(everything(), names_to = "param", values_to = "value") %>%
  mutate(
    legislator = as.integer(str_extract(param, "(?<=\\[)\\d+"))
  )

# 3. Because ideal points are only identified up to
#    a sign‐flip, we “align” each draw to the first draw:
theta_aligned <- theta_long %>%
  group_by(.draw = rep(1:(n()/n_distinct(legislator)), each = n_distinct(legislator))) %>%
  mutate(
    flip = if_else(cor(value, first(value)) < 0, -1, 1),
    value_aligned = value * flip
  ) %>%
  ungroup()

# 4. Summarize
theta_summary <- theta_aligned %>%
  group_by(legislator) %>%
  summarize(
    mean  = mean(value_aligned),
    lower = quantile(value_aligned, 0.025),
    upper = quantile(value_aligned, 0.975)
  )






